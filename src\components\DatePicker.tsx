import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FC, useState } from "react";
import moment from "moment";
import { FaCalendarAlt } from "react-icons/fa";

interface DatePickerProps {
  value: Date | undefined;
  onChange: ((date: Date | undefined) => void) | undefined;
  selectionType?: "date" | "month" | "year" | "recurringDate";
  fromDate?: Date;
  placeholder?: string;
  toDate?: Date;
  className?: string;
  disabled?: boolean;
}

const DatePicker: FC<DatePickerProps> = ({
  onChange,
  value = undefined,
  selectionType,
  fromDate = undefined,
  placeholder = "Select Date",
  toDate = undefined,
  className,
  disabled,
}) => {
  // SET MONTH AND YEAR
  const [year, setYear] = useState<string | undefined>(moment().format("YYYY"));
  const [defaultMonth, setDefaultMonth] = useState<Date | undefined>(moment().toDate());
  const [open, setOpen] = useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "w-[280px] justify-start border-primary-gray bg-primary-gray/10 text-left font-normal text-black hover:bg-primary-gray/10 hover:text-black active:bg-primary-gray/10 active:text-black",
            !value && "text-muted-foreground",
            disabled && "cursor-not-allowed opacity-50",
            className,
          )}
          disabled={disabled}
          onClick={() => setOpen(!open)}
        >
          <FaCalendarAlt className="mr-2 h-4 w-4" />
          {value ? selectionType === "recurringDate" ? moment(value).format("MMMM DD") : format(value, "PPP") : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <menu className="flex w-full flex-col gap-2 p-2">
          <ul className={`grid w-full gap-3 ${selectionType === "recurringDate" ? "grid-cols-1" : "grid-cols-2"}`}>
            {selectionType !== "recurringDate" && (
              <Select
                onValueChange={(e: string) => {
                  setYear(e);
                  setDefaultMonth(moment(e, "YYYY-MM-DD").toDate());
                }}
                value={year || ""}
              >
                <SelectTrigger className="!h-8">
                  <SelectValue placeholder="Year" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 200 }, (_, i) => (
                    <SelectItem key={String(2099 - i)} value={String(2099 - i)}>
                      {String(2099 - i)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            <Select
              onValueChange={(e: string) => {
                setDefaultMonth(moment(`${year}-${e}`, "YYYY-MM-DD").toDate());
              }}
              value={moment(defaultMonth).format("MM")}
            >
              <SelectTrigger className="!h-8">
                <SelectValue placeholder="Month" />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 12 }, (_, i) => (
                  <SelectItem key={String(i + 1).padStart(2, "0")} value={String(i + 1).padStart(2, "0")}>
                    {moment().month(i).format("MMM")}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </ul>
          <Calendar
            fromDate={fromDate}
            toDate={toDate}
            mode="single"
            month={defaultMonth}
            onMonthChange={e => {
              if (selectionType !== "recurringDate") {
                setDefaultMonth(e);
              }
            }}
            selected={value}
            onSelect={e => {
              if (e) {
                // Check the type of onChange and handle accordingly
                if (typeof onChange === "function") {
                  // Create a proper date object from the selected date
                  const dateObj = new Date(e);
                  onChange(dateObj);
                  setOpen(false);
                }
              }
            }}
            initialFocus
          />
        </menu>
      </PopoverContent>
    </Popover>
  );
};

export default DatePicker;
